# Exemplos de Uso do Sistema de Chat

O sistema de chat agora segue o mesmo padrão do games controller, com um ponto de entrada único para simplificar a configuração do API Gateway.

## 1. Enviar uma Mensagem

```bash
curl -X POST http://localhost:3000/chat/ \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer SEU_JWT_TOKEN" \
  -d '{
    "action": "sendMessage",
    "authorization": "Bearer SEU_JWT_TOKEN",
    "connectionId": "connection-id-opcional",
    "data": {
      "action": "sendMessage",
      "receiverIdentifier": "user123",
      "content": "Olá! Como você está?"
    }
  }'
```

**Ou usando o endpoint específico:**

```bash
curl -X POST http://localhost:3000/chat/send \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer SEU_JWT_TOKEN" \
  -d '{
    "action": "sendMessage",
    "authorization": "Bearer SEU_JWT_TOKEN",
    "connectionId": "connection-id-opcional",
    "data": {
      "action": "sendMessage",
      "receiverIdentifier": "user123",
      "content": "Olá! Como você está?"
    }
  }'
```

**Resposta:**
```json
{
  "id": 1,
  "senderIdentifier": "user456",
  "receiverIdentifier": "user123",
  "content": "Olá! Como você está?",
  "created_at": "2024-01-01T10:00:00Z",
  "is_read": false,
  "sender": {
    "id": 456,
    "identifier": "user456",
    "name": "João",
    "avatar": "avatar.jpg"
  },
  "receiver": {
    "id": 123,
    "identifier": "user123",
    "name": "Maria",
    "avatar": "avatar2.jpg"
  }
}
```

## 2. Listar Conversas

```bash
curl -X POST http://localhost:3000/chat/ \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer SEU_JWT_TOKEN" \
  -d '{
    "action": "getConversations",
    "authorization": "Bearer SEU_JWT_TOKEN",
    "connectionId": "connection-id-opcional",
    "data": {
      "action": "getConversations"
    }
  }'
```

**Resposta:**
```json
[
  {
    "userIdentifier": "user123",
    "userName": "Maria",
    "userAvatar": "avatar2.jpg",
    "lastMessage": {
      "id": 1,
      "senderIdentifier": "user456",
      "receiverIdentifier": "user123",
      "content": "Olá! Como você está?",
      "created_at": "2024-01-01T10:00:00Z",
      "is_read": false
    },
    "unreadCount": 3
  }
]
```

## 3. Listar Mensagens com Paginação

```bash
curl -X POST http://localhost:3000/chat/ \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer SEU_JWT_TOKEN" \
  -d '{
    "action": "getMessages",
    "authorization": "Bearer SEU_JWT_TOKEN",
    "connectionId": "connection-id-opcional",
    "data": {
      "action": "getMessages",
      "page": 1,
      "limit": 20
    }
  }'
```

## 4. Mensagens entre Dois Usuários

```bash
curl -X POST http://localhost:3000/chat/ \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer SEU_JWT_TOKEN" \
  -d '{
    "action": "getMessagesBetweenUsers",
    "authorization": "Bearer SEU_JWT_TOKEN",
    "connectionId": "connection-id-opcional",
    "data": {
      "action": "getMessagesBetweenUsers",
      "otherUserIdentifier": "user123",
      "page": 1,
      "limit": 20
    }
  }'
```

## 5. Marcar Mensagens como Lidas

```bash
curl -X POST http://localhost:3000/chat/ \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer SEU_JWT_TOKEN" \
  -d '{
    "action": "markAsRead",
    "authorization": "Bearer SEU_JWT_TOKEN",
    "connectionId": "connection-id-opcional",
    "data": {
      "action": "markAsRead",
      "messageIds": [1, 2, 3]
    }
  }'
```

## 6. Contagem de Mensagens Não Lidas

```bash
curl -X POST http://localhost:3000/chat/ \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer SEU_JWT_TOKEN" \
  -d '{
    "action": "getUnreadCount",
    "authorization": "Bearer SEU_JWT_TOKEN",
    "connectionId": "connection-id-opcional",
    "data": {
      "action": "getUnreadCount"
    }
  }'
```

**Resposta:**
```json
{
  "unreadCount": 5
}
```

## Fluxo de Funcionamento

### Quando uma mensagem é enviada:

1. **Validação**: Sistema verifica se sender e receiver existem
2. **Persistência**: Mensagem é salva no banco de dados
3. **Tentativa WebSocket**: Se o usuário estiver online (connectionId ativo), tenta enviar via WebSocket
4. **Fallback Push**: Se WebSocket falhar, envia push notification via Firebase
5. **Resposta**: Retorna a mensagem criada com dados completos

### Evento WebSocket Recebido:

```json
{
  "action": "chat_message_received",
  "data": {
    "id": 1,
    "senderId": 456,
    "receiverId": 123,
    "content": "Olá! Como você está?",
    "created_at": "2024-01-01T10:00:00Z",
    "is_read": false,
    "sender": {
      "id": 456,
      "name": "João",
      "avatar": "avatar.jpg"
    }
  }
}
```

## Integração com Frontend

### JavaScript/TypeScript

```javascript
// Enviar mensagem
const sendMessage = async (receiverId, content) => {
  const response = await fetch('/v1/chat/send', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'x-token': localStorage.getItem('token')
    },
    body: JSON.stringify({ receiverId, content })
  });
  return response.json();
};

// Listar conversas
const getConversations = async () => {
  const response = await fetch('/v1/chat/conversations', {
    headers: {
      'x-token': localStorage.getItem('token')
    }
  });
  return response.json();
};

// WebSocket para receber mensagens em tempo real
const socket = new WebSocket('wss://your-websocket-url');
socket.onmessage = (event) => {
  const data = JSON.parse(event.data);
  if (data.action === 'chat_message_received') {
    // Atualizar UI com nova mensagem
    console.log('Nova mensagem:', data.data);
  }
};
```

## Configuração Necessária

### 1. Migration Segura do Banco de Dados

**IMPORTANTE**: Esta migration é segura e NÃO altera a tabela `users` existente.

**Opção A - Migration Automática (Recomendada):**
```bash
npx prisma migrate dev --name add_chat_messages
```

**Opção B - Migration Manual (Se preferir controle total):**
```bash
# Execute o SQL fornecido no arquivo migration_chat_messages.sql
mysql -u seu_usuario -p sua_base_de_dados < migration_chat_messages.sql
```

### 2. Variáveis de Ambiente

Certifique-se de que as seguintes variáveis estão no `.env`:

```env
# WebSocket
AWS_SOCKET_API_URL=wss://your-api-gateway-url
AWS_REGION=us-east-1

# Firebase para Push Notifications
FIREBASE_CONF={"type":"service_account",...}

# JWT
JWT_SECRET=your-jwt-secret

# Database
DATABASE_URL=mysql://user:password@host:port/database
```

## Estrutura da Tabela

A tabela `chat_messages` será criada com a seguinte estrutura:

```sql
CREATE TABLE chat_messages (
  id INT PRIMARY KEY AUTO_INCREMENT,
  senderIdentifier VARCHAR(255) NOT NULL,
  receiverIdentifier VARCHAR(255) NOT NULL,
  content TEXT NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  is_read BOOLEAN DEFAULT FALSE,

  FOREIGN KEY (senderIdentifier) REFERENCES users(identifier) ON DELETE CASCADE,
  FOREIGN KEY (receiverIdentifier) REFERENCES users(identifier) ON DELETE CASCADE,

  INDEX idx_senderIdentifier (senderIdentifier),
  INDEX idx_receiverIdentifier (receiverIdentifier),
  INDEX idx_created_at (created_at),
  INDEX idx_is_read (is_read)
);
```

**Características da Migration:**
- ✅ **Segura**: Não altera tabela `users` existente
- ✅ **Não destrutiva**: Apenas adiciona nova tabela
- ✅ **Integridade**: Foreign keys garantem consistência
- ✅ **Performance**: Índices otimizados para consultas
