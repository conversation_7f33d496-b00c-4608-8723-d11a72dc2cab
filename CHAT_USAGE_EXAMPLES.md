# Exemplos de Uso do Sistema de Chat

## 1. Enviar uma <PERSON>

```bash
curl -X POST http://localhost:3000/v1/chat/send \
  -H "Content-Type: application/json" \
  -H "x-token: SEU_JWT_TOKEN" \
  -d '{
    "receiverId": 123,
    "content": "Ol<PERSON>! Como você está?"
  }'
```

**Resposta:**
```json
{
  "id": 1,
  "senderId": 456,
  "receiverId": 123,
  "content": "Ol<PERSON>! Como você está?",
  "created_at": "2024-01-01T10:00:00Z",
  "is_read": false,
  "sender": {
    "id": 456,
    "name": "<PERSON>",
    "avatar": "avatar.jpg"
  },
  "receiver": {
    "id": 123,
    "name": "<PERSON>",
    "avatar": "avatar2.jpg"
  }
}
```

## 2. Listar Conversas

```bash
curl -X GET http://localhost:3000/v1/chat/conversations \
  -H "x-token: SEU_JWT_TOKEN"
```

**Resposta:**
```json
[
  {
    "userId": 123,
    "userName": "<PERSON>",
    "userAvatar": "avatar2.jpg",
    "lastMessage": {
      "id": 1,
      "senderId": 456,
      "receiverId": 123,
      "content": "Olá! Como você está?",
      "created_at": "2024-01-01T10:00:00Z",
      "is_read": false
    },
    "unreadCount": 3
  }
]
```

## 3. Listar Mensagens com Paginação

```bash
curl -X GET "http://localhost:3000/v1/chat/messages?page=1&limit=20" \
  -H "x-token: SEU_JWT_TOKEN"
```

## 4. Mensagens entre Dois Usuários

```bash
curl -X GET "http://localhost:3000/v1/chat/messages/123?page=1&limit=20" \
  -H "x-token: SEU_JWT_TOKEN"
```

## 5. Marcar Mensagens como Lidas

```bash
curl -X POST http://localhost:3000/v1/chat/mark-read \
  -H "Content-Type: application/json" \
  -H "x-token: SEU_JWT_TOKEN" \
  -d '{
    "messageIds": [1, 2, 3]
  }'
```

## 6. Contagem de Mensagens Não Lidas

```bash
curl -X GET http://localhost:3000/v1/chat/unread-count \
  -H "x-token: SEU_JWT_TOKEN"
```

**Resposta:**
```json
{
  "unreadCount": 5
}
```

## Fluxo de Funcionamento

### Quando uma mensagem é enviada:

1. **Validação**: Sistema verifica se sender e receiver existem
2. **Persistência**: Mensagem é salva no banco de dados
3. **Tentativa WebSocket**: Se o usuário estiver online (connectionId ativo), tenta enviar via WebSocket
4. **Fallback Push**: Se WebSocket falhar, envia push notification via Firebase
5. **Resposta**: Retorna a mensagem criada com dados completos

### Evento WebSocket Recebido:

```json
{
  "action": "chat_message_received",
  "data": {
    "id": 1,
    "senderId": 456,
    "receiverId": 123,
    "content": "Olá! Como você está?",
    "created_at": "2024-01-01T10:00:00Z",
    "is_read": false,
    "sender": {
      "id": 456,
      "name": "João",
      "avatar": "avatar.jpg"
    }
  }
}
```

## Integração com Frontend

### JavaScript/TypeScript

```javascript
// Enviar mensagem
const sendMessage = async (receiverId, content) => {
  const response = await fetch('/v1/chat/send', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'x-token': localStorage.getItem('token')
    },
    body: JSON.stringify({ receiverId, content })
  });
  return response.json();
};

// Listar conversas
const getConversations = async () => {
  const response = await fetch('/v1/chat/conversations', {
    headers: {
      'x-token': localStorage.getItem('token')
    }
  });
  return response.json();
};

// WebSocket para receber mensagens em tempo real
const socket = new WebSocket('wss://your-websocket-url');
socket.onmessage = (event) => {
  const data = JSON.parse(event.data);
  if (data.action === 'chat_message_received') {
    // Atualizar UI com nova mensagem
    console.log('Nova mensagem:', data.data);
  }
};
```

## Configuração Necessária

Certifique-se de que as seguintes variáveis estão no `.env`:

```env
# WebSocket
AWS_SOCKET_API_URL=wss://your-api-gateway-url
AWS_REGION=us-east-1

# Firebase para Push Notifications
FIREBASE_CONF={"type":"service_account",...}

# JWT
JWT_SECRET=your-jwt-secret

# Database
DATABASE_URL=mysql://user:password@host:port/database
```

## Estrutura da Tabela

```sql
CREATE TABLE chat_messages (
  id INT PRIMARY KEY AUTO_INCREMENT,
  senderId BIGINT UNSIGNED NOT NULL,
  receiverId BIGINT UNSIGNED NOT NULL,
  content TEXT NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  is_read BOOLEAN DEFAULT FALSE,
  
  FOREIGN KEY (senderId) REFERENCES users(id) ON DELETE CASCADE,
  FOREIGN KEY (receiverId) REFERENCES users(id) ON DELETE CASCADE,
  
  INDEX idx_senderId (senderId),
  INDEX idx_receiverId (receiverId),
  INDEX idx_created_at (created_at),
  INDEX idx_is_read (is_read)
);
```
