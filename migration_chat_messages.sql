-- Migration para adicionar tabela chat_messages
-- Esta migration é segura e NÃO altera a tabela users existente

-- <PERSON><PERSON><PERSON> tabela chat_messages
CREATE TABLE IF NOT EXISTS `chat_messages` (
    `id` INT NOT NULL AUTO_INCREMENT,
    `senderIdentifier` VARCHAR(255) NOT NULL,
    `receiverIdentifier` VARCHAR(255) NOT NULL,
    `content` TEXT NOT NULL,
    `created_at` TIMESTAMP(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
    `is_read` BOOLEAN NOT NULL DEFAULT FALSE,

    PRIMARY KEY (`id`),

    -- Foreign keys para garantir integridade referencial
    CONSTRAINT `chat_messages_senderIdentifier_fkey`
        FOREIGN KEY (`senderIdentifier`) REFERENCES `users`(`identifier`) ON DELETE CASCADE,
    CONSTRAINT `chat_messages_receiverIdentifier_fkey`
        FOREIGN KEY (`receiverIdentifier`) REFERENCES `users`(`identifier`) ON DELETE CASCADE,

    -- Índices para performance
    INDEX `chat_messages_senderIdentifier_idx` (`senderIdentifier`),
    INDEX `chat_messages_receiverIdentifier_idx` (`receiverIdentifier`),
    INDEX `chat_messages_created_at_idx` (`created_at`),
    INDEX `chat_messages_is_read_idx` (`is_read`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Verificar se a tabela foi criada corretamente
SELECT 'Tabela chat_messages criada com sucesso!' as status;
