-- Migration para adicionar tabela chat_messages
-- Esta migration é segura e NÃO altera a tabela users existente

-- <PERSON><PERSON><PERSON> tabela chat_messages
CREATE TABLE IF NOT EXISTS `chat_messages` (
    `id` INT NOT NULL AUTO_INCREMENT,
    `senderId` BIGINT UNSIGNED NOT NULL,
    `receiverId` BIGINT UNSIGNED NOT NULL,
    `content` TEXT NOT NULL,
    `created_at` TIMESTAMP(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0),
    `is_read` BOOLEAN NOT NULL DEFAULT FALSE,
    
    PRIMARY KEY (`id`),
    
    -- Foreign keys para garantir integridade referencial
    CONSTRAINT `chat_messages_senderId_fkey` 
        FOREIGN KEY (`senderId`) REFERENCES `users`(`id`) ON DELETE CASCADE,
    CONSTRAINT `chat_messages_receiverId_fkey` 
        FOREIGN KEY (`receiverId`) REFERENCES `users`(`id`) ON DELETE CASCADE,
    
    -- Índices para performance
    INDEX `chat_messages_senderId_idx` (`senderId`),
    INDEX `chat_messages_receiverId_idx` (`receiverId`),
    INDEX `chat_messages_created_at_idx` (`created_at`),
    INDEX `chat_messages_is_read_idx` (`is_read`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Verificar se a tabela foi criada corretamente
SELECT 'Tabela chat_messages criada com sucesso!' as status;
