import { Injectable } from '@nestjs/common';
import { PrismaService } from 'src/prisma/prisma.service';
import * as admin from 'firebase-admin';
import showDebugConsole from 'src/shared/utils/show-debug-console';

@Injectable()
export class PushNotificationService {
  constructor(private readonly prismaService: PrismaService) {}

  async sendChatNotification(
    receiverId: number,
    senderName: string,
    message: string,
  ): Promise<boolean> {
    try {
      // Buscar o onesignal_id do usuário
      const user = await this.prismaService.users.findUnique({
        where: { id: BigInt(receiverId) },
        select: { onesignal_id: true, name: true },
      });

      if (!user?.onesignal_id) {
        showDebugConsole(`User ${receiverId} has no OneSignal ID`);
        return false;
      }

      // Enviar notificação via Firebase
      const payload = {
        notification: {
          title: `Nova mensagem de ${senderName}`,
          body: message.length > 100 ? message.substring(0, 100) + '...' : message,
        },
        data: {
          type: 'chat_message',
          senderId: receiverId.toString(),
          senderName: senderName,
        },
        token: user.onesignal_id,
      };

      const response = await admin.messaging().send(payload);
      showDebugConsole(`Push notification sent successfully: ${response}`);
      return true;
    } catch (error) {
      showDebugConsole(`Error sending push notification: ${error.message}`);
      return false;
    }
  }

  async sendBulkChatNotifications(
    notifications: Array<{
      receiverId: number;
      senderName: string;
      message: string;
    }>,
  ): Promise<{ success: number; failed: number }> {
    let success = 0;
    let failed = 0;

    for (const notification of notifications) {
      const result = await this.sendChatNotification(
        notification.receiverId,
        notification.senderName,
        notification.message,
      );
      
      if (result) {
        success++;
      } else {
        failed++;
      }
    }

    return { success, failed };
  }
}
