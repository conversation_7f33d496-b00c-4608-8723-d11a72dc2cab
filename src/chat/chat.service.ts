import { Injectable, NotFoundException } from '@nestjs/common';
import { CommandBus } from '@nestjs/cqrs';
import { PrismaService } from '../prisma/prisma.service';
import { PushNotificationService } from './services/push-notification.service';
import { SendChatMessageCommand } from './commands/send-chat-message.command';
import SendMessageDTO from './models/dtos/send-message.dto';
import GetMessagesDTO from './models/dtos/get-messages.dto';
import MarkAsReadDTO from './models/dtos/mark-as-read.dto';
import ChatMessageModel, {
  ChatConversationModel,
  ChatMessageResponse
} from './models/chat-message.model';
import { ChatDataDTO } from './models/dtos/chat-message.dto';
import showDebugConsole from '../shared/utils/show-debug-console';
import { verify } from 'jsonwebtoken';
import { JwtPayload } from '../shared/types/jwt-payload.type';

@Injectable()
export class ChatService {
  constructor(
    private readonly prismaService: PrismaService,
    private readonly pushNotificationService: PushNotificationService,
    private readonly commandBus: CommandBus,
  ) {}

  private getUserFromToken(token: string): any {
    try {
      const decoded = verify(token, process.env.JWT_SECRET) as JwtPayload;
      return decoded.user;
    } catch (error) {
      throw new NotFoundException('Invalid token');
    }
  }

  // Método principal para processar ações do chat
  async processAction(params: ChatDataDTO<any>): Promise<any> {
    const user = this.getUserFromToken(params.token);
    const userIdentifier = user.identifier;

    switch (params.data.action) {
      case 'sendMessage':
        return await this.sendMessage(userIdentifier, params.data);
      case 'getMessages':
        return await this.getMessages(userIdentifier, params.data);
      case 'getConversations':
        return await this.getConversations(userIdentifier);
      case 'markAsRead':
        return await this.markAsRead(userIdentifier, params.data);
      case 'getUnreadCount':
        return await this.getUnreadCount(userIdentifier);
      case 'getMessagesBetweenUsers':
        return await this.getMessagesBetweenUsers(
          userIdentifier,
          params.data.otherUserIdentifier,
          params.data.page,
          params.data.limit,
        );
      default:
        throw new NotFoundException('Action not found');
    }
  }

  async sendMessage(
    senderIdentifier: string,
    messageData: SendMessageDTO,
  ): Promise<ChatMessageModel> {
    // Verificar se os usuários existem
    const [sender, receiver] = await Promise.all([
      this.prismaService.users.findUnique({
        where: { identifier: senderIdentifier },
        select: { id: true, identifier: true, name: true, avatar: true },
      }),
      this.prismaService.users.findUnique({
        where: { identifier: messageData.receiverIdentifier },
        select: { id: true, identifier: true, name: true, avatar: true },
      }),
    ]);

    if (!sender) {
      throw new NotFoundException('Sender not found');
    }

    if (!receiver) {
      throw new NotFoundException('Receiver not found');
    }

    // Criar a mensagem no banco
    const message = await this.prismaService.chat_messages.create({
      data: {
        senderIdentifier: senderIdentifier,
        receiverIdentifier: messageData.receiverIdentifier,
        content: messageData.content,
      },
    });

    const chatMessage: ChatMessageModel = {
      id: message.id,
      senderIdentifier: message.senderIdentifier,
      receiverIdentifier: message.receiverIdentifier,
      content: message.content,
      created_at: message.created_at,
      is_read: message.is_read,
      sender: {
        id: Number(sender.id),
        identifier: sender.identifier,
        name: sender.name,
        avatar: sender.avatar,
      },
      receiver: {
        id: Number(receiver.id),
        identifier: receiver.identifier,
        name: receiver.name,
        avatar: receiver.avatar,
      },
    };

    // Tentar enviar via socket primeiro
    const receiverConnectionId = await this.getUserConnectionId(Number(receiver.id));
    let socketSent = false;

    if (receiverConnectionId) {
      try {
        socketSent = await this.commandBus.execute(
          new SendChatMessageCommand(chatMessage, receiverConnectionId),
        );
      } catch (error) {
        showDebugConsole(`Socket send failed: ${error.message}`);
      }
    }

    // Se não conseguiu enviar via socket, enviar push notification
    if (!socketSent) {
      showDebugConsole('Sending push notification as fallback');
      await this.pushNotificationService.sendChatNotification(
        Number(receiver.id),
        sender.name,
        messageData.content,
      );
    }

    return chatMessage;
  }

  async getMessages(userIdentifier: string, params: GetMessagesDTO): Promise<ChatMessageResponse> {
    const page = params.page || 1;
    const limit = params.limit || 20;
    const skip = (page - 1) * limit;

    const [messages, total] = await Promise.all([
      this.prismaService.chat_messages.findMany({
        where: {
          OR: [
            { senderIdentifier: userIdentifier },
            { receiverIdentifier: userIdentifier },
          ],
        },
        orderBy: { created_at: 'desc' },
        skip,
        take: limit,
      }),
      this.prismaService.chat_messages.count({
        where: {
          OR: [
            { senderIdentifier: userIdentifier },
            { receiverIdentifier: userIdentifier },
          ],
        },
      }),
    ]);

    // Buscar dados dos usuários
    const userIdentifiers = Array.from(new Set([
      ...messages.map(m => m.senderIdentifier),
      ...messages.map(m => m.receiverIdentifier)
    ]));

    const users = await this.prismaService.users.findMany({
      where: { identifier: { in: userIdentifiers } },
      select: { id: true, identifier: true, name: true, avatar: true },
    });

    const usersMap = new Map(users.map(user => [user.identifier, user]));

    const chatMessages: ChatMessageModel[] = messages.map((message) => {
      const sender = usersMap.get(message.senderIdentifier);
      const receiver = usersMap.get(message.receiverIdentifier);

      return {
        id: message.id,
        senderIdentifier: message.senderIdentifier,
        receiverIdentifier: message.receiverIdentifier,
        content: message.content,
        created_at: message.created_at,
        is_read: message.is_read,
        sender: sender ? {
          id: Number(sender.id),
          identifier: sender.identifier,
          name: sender.name,
          avatar: sender.avatar,
        } : undefined,
        receiver: receiver ? {
          id: Number(receiver.id),
          identifier: receiver.identifier,
          name: receiver.name,
          avatar: receiver.avatar,
        } : undefined,
      };
    });

    return {
      messages: chatMessages,
      total,
      page,
      limit,
      hasMore: skip + limit < total,
    };
  }

  async getConversations(userIdentifier: string): Promise<ChatConversationModel[]> {
    // Buscar todas as conversas do usuário
    const conversations = await this.prismaService.$queryRaw<any[]>`
      SELECT
        CASE
          WHEN cm.senderIdentifier = ${userIdentifier} THEN cm.receiverIdentifier
          ELSE cm.senderIdentifier
        END as userIdentifier,
        u.name as userName,
        u.avatar as userAvatar,
        cm.id as lastMessageId,
        cm.content as lastMessageContent,
        cm.created_at as lastMessageDate,
        cm.senderIdentifier as lastMessageSenderIdentifier,
        cm.receiverIdentifier as lastMessageReceiverIdentifier,
        cm.is_read as lastMessageIsRead,
        (
          SELECT COUNT(*)
          FROM chat_messages cm2
          WHERE cm2.senderIdentifier != ${userIdentifier}
            AND cm2.receiverIdentifier = ${userIdentifier}
            AND cm2.is_read = false
            AND (
              (cm2.senderIdentifier = cm.senderIdentifier AND cm2.receiverIdentifier = cm.receiverIdentifier) OR
              (cm2.senderIdentifier = cm.receiverIdentifier AND cm2.receiverIdentifier = cm.senderIdentifier)
            )
        ) as unreadCount
      FROM chat_messages cm
      INNER JOIN users u ON (
        CASE
          WHEN cm.senderIdentifier = ${userIdentifier} THEN u.identifier = cm.receiverIdentifier
          ELSE u.identifier = cm.senderIdentifier
        END
      )
      WHERE cm.senderIdentifier = ${userIdentifier} OR cm.receiverIdentifier = ${userIdentifier}
      AND cm.id IN (
        SELECT MAX(cm3.id)
        FROM chat_messages cm3
        WHERE (cm3.senderIdentifier = ${userIdentifier} OR cm3.receiverIdentifier = ${userIdentifier})
        GROUP BY
          CASE
            WHEN cm3.senderIdentifier = ${userIdentifier} THEN cm3.receiverIdentifier
            ELSE cm3.senderIdentifier
          END
      )
      ORDER BY cm.created_at DESC
    `;

    return conversations.map((conv) => ({
      userIdentifier: conv.userIdentifier,
      userName: conv.userName,
      userAvatar: conv.userAvatar,
      lastMessage: {
        id: conv.lastMessageId,
        senderIdentifier: conv.lastMessageSenderIdentifier,
        receiverIdentifier: conv.lastMessageReceiverIdentifier,
        content: conv.lastMessageContent,
        created_at: conv.lastMessageDate,
        is_read: conv.lastMessageIsRead,
      },
      unreadCount: Number(conv.unreadCount),
    }));
  }

  async markAsRead(userIdentifier: string, messageData: MarkAsReadDTO): Promise<number> {
    const result = await this.prismaService.chat_messages.updateMany({
      where: {
        id: { in: messageData.messageIds },
        receiverIdentifier: userIdentifier,
        is_read: false,
      },
      data: {
        is_read: true,
      },
    });

    return result.count;
  }

  async getUnreadCount(userIdentifier: string): Promise<number> {
    return await this.prismaService.chat_messages.count({
      where: {
        receiverIdentifier: userIdentifier,
        is_read: false,
      },
    });
  }

  private async getUserConnectionId(userId: number): Promise<string | null> {
    try {
      // Buscar o connectionId do usuário na tabela game_users
      const gameUser = await this.prismaService.game_users.findFirst({
        where: {
          userId: BigInt(userId),
          isConnected: true,
        },
        select: { connectionId: true },
        orderBy: { updated_at: 'desc' },
      });

      return gameUser?.connectionId || null;
    } catch (error) {
      showDebugConsole(`Error getting user connection ID: ${error.message}`);
      return null;
    }
  }

  async getMessagesBetweenUsers(
    userIdentifier1: string,
    userIdentifier2: string,
    page: number = 1,
    limit: number = 20,
  ): Promise<ChatMessageResponse> {
    const skip = (page - 1) * limit;

    const [messages, total] = await Promise.all([
      this.prismaService.chat_messages.findMany({
        where: {
          OR: [
            { senderIdentifier: userIdentifier1, receiverIdentifier: userIdentifier2 },
            { senderIdentifier: userIdentifier2, receiverIdentifier: userIdentifier1 },
          ],
        },
        orderBy: { created_at: 'desc' },
        skip,
        take: limit,
      }),
      this.prismaService.chat_messages.count({
        where: {
          OR: [
            { senderIdentifier: userIdentifier1, receiverIdentifier: userIdentifier2 },
            { senderIdentifier: userIdentifier2, receiverIdentifier: userIdentifier1 },
          ],
        },
      }),
    ]);

    // Buscar dados dos usuários
    const users = await this.prismaService.users.findMany({
      where: { identifier: { in: [userIdentifier1, userIdentifier2] } },
      select: { id: true, identifier: true, name: true, avatar: true },
    });

    const usersMap = new Map(users.map(user => [user.identifier, user]));

    const chatMessages: ChatMessageModel[] = messages.map((message) => {
      const sender = usersMap.get(message.senderIdentifier);
      const receiver = usersMap.get(message.receiverIdentifier);

      return {
        id: message.id,
        senderIdentifier: message.senderIdentifier,
        receiverIdentifier: message.receiverIdentifier,
        content: message.content,
        created_at: message.created_at,
        is_read: message.is_read,
        sender: sender ? {
          id: Number(sender.id),
          identifier: sender.identifier,
          name: sender.name,
          avatar: sender.avatar,
        } : undefined,
        receiver: receiver ? {
          id: Number(receiver.id),
          identifier: receiver.identifier,
          name: receiver.name,
          avatar: receiver.avatar,
        } : undefined,
      };
    });

    return {
      messages: chatMessages,
      total,
      page,
      limit,
      hasMore: skip + limit < total,
    };
  }
}
