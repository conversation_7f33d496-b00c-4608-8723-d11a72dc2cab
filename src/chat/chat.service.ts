import { Injectable, NotFoundException } from '@nestjs/common';
import { CommandBus } from '@nestjs/cqrs';
import { PrismaService } from '../prisma/prisma.service';
import { PushNotificationService } from './services/push-notification.service';
import { SendChatMessageCommand } from './commands/send-chat-message.command';
import SendMessageDTO from './models/dtos/send-message.dto';
import GetMessagesDTO from './models/dtos/get-messages.dto';
import MarkAsReadDTO from './models/dtos/mark-as-read.dto';
import ChatMessageModel, {
  ChatConversationModel,
  ChatMessageResponse
} from './models/chat-message.model';
import { ChatDataDTO } from './models/dtos/chat-message.dto';
import showDebugConsole from '../shared/utils/show-debug-console';
import { verify } from 'jsonwebtoken';
import { JwtPayload } from '../shared/types/jwt-payload.type';

@Injectable()
export class ChatService {
  constructor(
    private readonly prismaService: PrismaService,
    private readonly pushNotificationService: PushNotificationService,
    private readonly commandBus: CommandBus,
  ) {}

  private getUserFromToken(token: string): any {
    try {
      const decoded = verify(token, process.env.JWT_SECRET) as JwtPayload;
      return decoded.user;
    } catch (error) {
      throw new NotFoundException('Invalid token');
    }
  }

  // Método principal para processar ações do chat
  async processAction(params: ChatDataDTO<any>): Promise<any> {
    const user = this.getUserFromToken(params.token);
    const userId = Number(user.id);

    switch (params.data.action) {
      case 'sendMessage':
        return await this.sendMessage(userId, params.data);
      case 'getMessages':
        return await this.getMessages(userId, params.data);
      case 'getConversations':
        return await this.getConversations(userId);
      case 'markAsRead':
        return await this.markAsRead(userId, params.data);
      case 'getUnreadCount':
        return await this.getUnreadCount(userId);
      case 'getMessagesBetweenUsers':
        return await this.getMessagesBetweenUsers(
          userId,
          params.data.otherUserId,
          params.data.page,
          params.data.limit,
        );
      default:
        throw new NotFoundException('Action not found');
    }
  }

  async sendMessage(
    senderId: number,
    messageData: SendMessageDTO,
  ): Promise<ChatMessageModel> {
    // Verificar se os usuários existem
    const [sender, receiver] = await Promise.all([
      this.prismaService.users.findUnique({
        where: { id: BigInt(senderId) },
        select: { id: true, name: true, avatar: true },
      }),
      this.prismaService.users.findUnique({
        where: { id: BigInt(messageData.receiverId) },
        select: { id: true, name: true, avatar: true },
      }),
    ]);

    if (!sender) {
      throw new NotFoundException('Sender not found');
    }

    if (!receiver) {
      throw new NotFoundException('Receiver not found');
    }

    // Criar a mensagem no banco
    const message = await this.prismaService.chat_messages.create({
      data: {
        senderId: BigInt(senderId),
        receiverId: BigInt(messageData.receiverId),
        content: messageData.content,
      },
      include: {
        sender: {
          select: { id: true, name: true, avatar: true },
        },
        receiver: {
          select: { id: true, name: true, avatar: true },
        },
      },
    });

    const chatMessage: ChatMessageModel = {
      id: message.id,
      senderId: Number(message.senderId),
      receiverId: Number(message.receiverId),
      content: message.content,
      created_at: message.created_at,
      is_read: message.is_read,
      sender: {
        id: Number(message.sender.id),
        name: message.sender.name,
        avatar: message.sender.avatar,
      },
      receiver: {
        id: Number(message.receiver.id),
        name: message.receiver.name,
        avatar: message.receiver.avatar,
      },
    };

    // Tentar enviar via socket primeiro
    const receiverConnectionId = await this.getUserConnectionId(messageData.receiverId);
    let socketSent = false;

    if (receiverConnectionId) {
      try {
        socketSent = await this.commandBus.execute(
          new SendChatMessageCommand(chatMessage, receiverConnectionId),
        );
      } catch (error) {
        showDebugConsole(`Socket send failed: ${error.message}`);
      }
    }

    // Se não conseguiu enviar via socket, enviar push notification
    if (!socketSent) {
      showDebugConsole('Sending push notification as fallback');
      await this.pushNotificationService.sendChatNotification(
        messageData.receiverId,
        sender.name,
        messageData.content,
      );
    }

    return chatMessage;
  }

  async getMessages(userId: number, params: GetMessagesDTO): Promise<ChatMessageResponse> {
    const page = params.page || 1;
    const limit = params.limit || 20;
    const skip = (page - 1) * limit;

    const [messages, total] = await Promise.all([
      this.prismaService.chat_messages.findMany({
        where: {
          OR: [
            { senderId: BigInt(userId) },
            { receiverId: BigInt(userId) },
          ],
        },
        include: {
          sender: {
            select: { id: true, name: true, avatar: true },
          },
          receiver: {
            select: { id: true, name: true, avatar: true },
          },
        },
        orderBy: { created_at: 'desc' },
        skip,
        take: limit,
      }),
      this.prismaService.chat_messages.count({
        where: {
          OR: [
            { senderId: BigInt(userId) },
            { receiverId: BigInt(userId) },
          ],
        },
      }),
    ]);

    const chatMessages: ChatMessageModel[] = messages.map((message) => ({
      id: message.id,
      senderId: Number(message.senderId),
      receiverId: Number(message.receiverId),
      content: message.content,
      created_at: message.created_at,
      is_read: message.is_read,
      sender: {
        id: Number(message.sender.id),
        name: message.sender.name,
        avatar: message.sender.avatar,
      },
      receiver: {
        id: Number(message.receiver.id),
        name: message.receiver.name,
        avatar: message.receiver.avatar,
      },
    }));

    return {
      messages: chatMessages,
      total,
      page,
      limit,
      hasMore: skip + limit < total,
    };
  }

  async getConversations(userId: number): Promise<ChatConversationModel[]> {
    // Buscar todas as conversas do usuário
    const conversations = await this.prismaService.$queryRaw<any[]>`
      SELECT
        CASE
          WHEN cm.senderId = ${BigInt(userId)} THEN cm.receiverId
          ELSE cm.senderId
        END as userId,
        u.name as userName,
        u.avatar as userAvatar,
        cm.id as lastMessageId,
        cm.content as lastMessageContent,
        cm.created_at as lastMessageDate,
        cm.senderId as lastMessageSenderId,
        cm.receiverId as lastMessageReceiverId,
        cm.is_read as lastMessageIsRead,
        (
          SELECT COUNT(*)
          FROM chat_messages cm2
          WHERE cm2.senderId != ${BigInt(userId)}
            AND cm2.receiverId = ${BigInt(userId)}
            AND cm2.is_read = false
            AND (
              (cm2.senderId = cm.senderId AND cm2.receiverId = cm.receiverId) OR
              (cm2.senderId = cm.receiverId AND cm2.receiverId = cm.senderId)
            )
        ) as unreadCount
      FROM chat_messages cm
      INNER JOIN users u ON (
        CASE
          WHEN cm.senderId = ${BigInt(userId)} THEN u.id = cm.receiverId
          ELSE u.id = cm.senderId
        END
      )
      WHERE cm.senderId = ${BigInt(userId)} OR cm.receiverId = ${BigInt(userId)}
      AND cm.id IN (
        SELECT MAX(cm3.id)
        FROM chat_messages cm3
        WHERE (cm3.senderId = ${BigInt(userId)} OR cm3.receiverId = ${BigInt(userId)})
        GROUP BY
          CASE
            WHEN cm3.senderId = ${BigInt(userId)} THEN cm3.receiverId
            ELSE cm3.senderId
          END
      )
      ORDER BY cm.created_at DESC
    `;

    return conversations.map((conv) => ({
      userId: Number(conv.userId),
      userName: conv.userName,
      userAvatar: conv.userAvatar,
      lastMessage: {
        id: conv.lastMessageId,
        senderId: Number(conv.lastMessageSenderId),
        receiverId: Number(conv.lastMessageReceiverId),
        content: conv.lastMessageContent,
        created_at: conv.lastMessageDate,
        is_read: conv.lastMessageIsRead,
      },
      unreadCount: Number(conv.unreadCount),
    }));
  }

  async markAsRead(userId: number, messageData: MarkAsReadDTO): Promise<number> {
    const result = await this.prismaService.chat_messages.updateMany({
      where: {
        id: { in: messageData.messageIds },
        receiverId: BigInt(userId),
        is_read: false,
      },
      data: {
        is_read: true,
      },
    });

    return result.count;
  }

  async getUnreadCount(userId: number): Promise<number> {
    return await this.prismaService.chat_messages.count({
      where: {
        receiverId: BigInt(userId),
        is_read: false,
      },
    });
  }

  private async getUserConnectionId(userId: number): Promise<string | null> {
    try {
      // Buscar o connectionId do usuário na tabela game_users
      const gameUser = await this.prismaService.game_users.findFirst({
        where: {
          userId: BigInt(userId),
          isConnected: true,
        },
        select: { connectionId: true },
        orderBy: { updated_at: 'desc' },
      });

      return gameUser?.connectionId || null;
    } catch (error) {
      showDebugConsole(`Error getting user connection ID: ${error.message}`);
      return null;
    }
  }

  async getMessagesBetweenUsers(
    userId1: number,
    userId2: number,
    page: number = 1,
    limit: number = 20,
  ): Promise<ChatMessageResponse> {
    const skip = (page - 1) * limit;

    const [messages, total] = await Promise.all([
      this.prismaService.chat_messages.findMany({
        where: {
          OR: [
            { senderId: BigInt(userId1), receiverId: BigInt(userId2) },
            { senderId: BigInt(userId2), receiverId: BigInt(userId1) },
          ],
        },
        include: {
          sender: {
            select: { id: true, name: true, avatar: true },
          },
          receiver: {
            select: { id: true, name: true, avatar: true },
          },
        },
        orderBy: { created_at: 'desc' },
        skip,
        take: limit,
      }),
      this.prismaService.chat_messages.count({
        where: {
          OR: [
            { senderId: BigInt(userId1), receiverId: BigInt(userId2) },
            { senderId: BigInt(userId2), receiverId: BigInt(userId1) },
          ],
        },
      }),
    ]);

    const chatMessages: ChatMessageModel[] = messages.map((message) => ({
      id: message.id,
      senderId: Number(message.senderId),
      receiverId: Number(message.receiverId),
      content: message.content,
      created_at: message.created_at,
      is_read: message.is_read,
      sender: {
        id: Number(message.sender.id),
        name: message.sender.name,
        avatar: message.sender.avatar,
      },
      receiver: {
        id: Number(message.receiver.id),
        name: message.receiver.name,
        avatar: message.receiver.avatar,
      },
    }));

    return {
      messages: chatMessages,
      total,
      page,
      limit,
      hasMore: skip + limit < total,
    };
  }
}
