import {
  Controller,
  Post,
  Get,
  Body,
  Query,
  UseGuards,
  Request,
  Param,
  ParseIntPipe,
} from '@nestjs/common';
import { ChatService } from './chat.service';
import { UserTokenAuth } from 'src/shared/guards/user-token-auth.guard';
import SendMessageDTO from './models/dtos/send-message.dto';
import GetMessagesDTO from './models/dtos/get-messages.dto';
import MarkAsReadDTO from './models/dtos/mark-as-read.dto';

@Controller('v1/chat')
@UseGuards(UserTokenAuth)
export class ChatController {
  constructor(private readonly chatService: ChatService) {}

  @Post('send')
  async sendMessage(@Request() req: any, @Body() messageData: SendMessageDTO) {
    const userId = Number(req.user.id);
    return await this.chatService.sendMessage(userId, messageData);
  }

  @Get('messages')
  async getMessages(@Request() req: any, @Query() query: GetMessagesDTO) {
    const userId = Number(req.user.id);
    const params: GetMessagesDTO = {
      userId,
      page: query.page ? Number(query.page) : 1,
      limit: query.limit ? Number(query.limit) : 20,
    };
    return await this.chatService.getMessages(params);
  }

  @Get('conversations')
  async getConversations(@Request() req: any) {
    const userId = Number(req.user.id);
    return await this.chatService.getConversations(userId);
  }

  @Post('mark-read')
  async markAsRead(@Request() req: any, @Body() messageData: MarkAsReadDTO) {
    const userId = Number(req.user.id);
    return {
      markedCount: await this.chatService.markAsRead(userId, messageData),
    };
  }

  @Get('unread-count')
  async getUnreadCount(@Request() req: any) {
    const userId = Number(req.user.id);
    return {
      unreadCount: await this.chatService.getUnreadCount(userId),
    };
  }

  @Get('messages/:otherUserId')
  async getMessagesBetweenUsers(
    @Request() req: any,
    @Param('otherUserId', ParseIntPipe) otherUserId: number,
    @Query('page') page?: string,
    @Query('limit') limit?: string,
  ) {
    const userId = Number(req.user.id);
    const pageNum = page ? Number(page) : 1;
    const limitNum = limit ? Number(limit) : 20;
    
    return await this.chatService.getMessagesBetweenUsers(
      userId,
      otherUserId,
      pageNum,
      limitNum,
    );
  }
}
