export interface ChatMessageModel {
  id: number;
  senderId: number;
  receiverId: number;
  content: string;
  created_at: Date;
  is_read: boolean;
  sender?: {
    id: number;
    name: string;
    avatar?: string;
  };
  receiver?: {
    id: number;
    name: string;
    avatar?: string;
  };
}

export interface ChatConversationModel {
  userId: number;
  userName: string;
  userAvatar?: string;
  lastMessage: ChatMessageModel;
  unreadCount: number;
}

export interface ChatMessageResponse {
  messages: ChatMessageModel[];
  total: number;
  page: number;
  limit: number;
  hasMore: boolean;
}

export default ChatMessageModel;
