# Sistema de Chat

Este módulo implementa um sistema completo de chat com as seguintes funcionalidades:

## Funcionalidades

- ✅ Envio e recebimento de mensagens
- ✅ Verificação de usuários online via WebSocket
- ✅ Fallback para push notifications quando usuário offline
- ✅ Listagem de mensagens com paginação
- ✅ Contagem de mensagens não lidas
- ✅ Marcar mensagens como lidas
- ✅ Listagem de conversas
- ✅ Validação de usuários existentes

## Endpoints da API

### POST `/v1/chat/send`
Envia uma nova mensagem.

**Body:**
```json
{
  "receiverId": 123,
  "content": "Olá! Como você está?"
}
```

**Response:**
```json
{
  "id": 1,
  "senderId": 456,
  "receiverId": 123,
  "content": "Olá! Como você está?",
  "created_at": "2024-01-01T10:00:00Z",
  "is_read": false,
  "sender": {
    "id": 456,
    "name": "<PERSON>",
    "avatar": "avatar.jpg"
  },
  "receiver": {
    "id": 123,
    "name": "<PERSON>",
    "avatar": "avatar2.jpg"
  }
}
```

### GET `/v1/chat/messages?page=1&limit=20`
Lista todas as mensagens do usuário (enviadas e recebidas).

### GET `/v1/chat/conversations`
Lista todas as conversas do usuário com a última mensagem e contagem de não lidas.

**Response:**
```json
[
  {
    "userId": 123,
    "userName": "Maria",
    "userAvatar": "avatar2.jpg",
    "lastMessage": {
      "id": 1,
      "senderId": 456,
      "receiverId": 123,
      "content": "Olá! Como você está?",
      "created_at": "2024-01-01T10:00:00Z",
      "is_read": false
    },
    "unreadCount": 3
  }
]
```

### POST `/v1/chat/mark-read`
Marca mensagens como lidas.

**Body:**
```json
{
  "messageIds": [1, 2, 3]
}
```

### GET `/v1/chat/unread-count`
Retorna a contagem total de mensagens não lidas.

**Response:**
```json
{
  "unreadCount": 5
}
```

### GET `/v1/chat/messages/:otherUserId?page=1&limit=20`
Lista mensagens entre dois usuários específicos.

## Fluxo de Funcionamento

1. **Envio de Mensagem:**
   - Valida se sender e receiver existem
   - Salva mensagem no banco de dados
   - Tenta enviar via WebSocket se usuário estiver online
   - Se falhar, envia push notification como fallback

2. **Verificação de Usuário Online:**
   - Busca connectionId na tabela `game_users` onde `isConnected = true`
   - Se encontrado, tenta enviar via AWS API Gateway WebSocket

3. **Push Notifications:**
   - Usa Firebase Admin SDK
   - Busca `onesignal_id` do usuário na tabela `users`
   - Envia notificação com título e preview da mensagem

## Estrutura do Banco de Dados

```sql
CREATE TABLE chat_messages (
  id INT PRIMARY KEY AUTO_INCREMENT,
  senderId BIGINT UNSIGNED NOT NULL,
  receiverId BIGINT UNSIGNED NOT NULL,
  content TEXT NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  is_read BOOLEAN DEFAULT FALSE,
  
  FOREIGN KEY (senderId) REFERENCES users(id) ON DELETE CASCADE,
  FOREIGN KEY (receiverId) REFERENCES users(id) ON DELETE CASCADE,
  
  INDEX idx_senderId (senderId),
  INDEX idx_receiverId (receiverId),
  INDEX idx_created_at (created_at),
  INDEX idx_is_read (is_read)
);
```

## Autenticação

Todos os endpoints requerem autenticação via `UserTokenAuth` guard. O token deve ser enviado via:
- Header: `x-token`
- Query parameter: `xToken`
- Body: `xToken`

## WebSocket Events

Quando uma mensagem é enviada via WebSocket, o evento enviado tem o formato:

```json
{
  "action": "chat_message_received",
  "data": {
    "id": 1,
    "senderId": 456,
    "receiverId": 123,
    "content": "Olá! Como você está?",
    "created_at": "2024-01-01T10:00:00Z",
    "is_read": false,
    "sender": {
      "id": 456,
      "name": "João",
      "avatar": "avatar.jpg"
    }
  }
}
```

## Configuração Necessária

Certifique-se de que as seguintes variáveis de ambiente estão configuradas:

- `AWS_SOCKET_API_URL` - URL do API Gateway WebSocket
- `AWS_REGION` - Região AWS
- `FIREBASE_CONF` - Configuração do Firebase para push notifications

## Testes

Execute os testes com:
```bash
npm run test src/chat/chat.service.spec.ts
```
