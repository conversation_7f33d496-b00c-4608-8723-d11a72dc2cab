import { <PERSON><PERSON><PERSON><PERSON>, ICommandHand<PERSON> } from '@nestjs/cqrs';
import { SendChatMessageCommand } from '../send-chat-message.command';
import {
  ApiGatewayManagementApiClient,
  PostToConnectionCommand,
} from '@aws-sdk/client-apigatewaymanagementapi';
import showDebugConsole from 'src/shared/utils/show-debug-console';
import bigIntReplacer from 'src/shared/utils/replacer';

@CommandHandler(SendChatMessageCommand)
export class SendChatMessageHandler
  implements ICommandHandler<SendChatMessageCommand>
{
  async execute(command: SendChatMessageCommand): Promise<boolean> {
    if (!command.receiverConnectionId) {
      showDebugConsole('No connection ID provided for chat message');
      return false;
    }

    try {
      showDebugConsole(
        `Sending chat message to connection: ${command.receiverConnectionId}`,
      );

      const client = new ApiGatewayManagementApiClient({
        endpoint: process.env.AWS_SOCKET_API_URL,
        region: process.env.AWS_REGION,
      });

      const messageData = {
        action: 'chat_message_received',
        data: command.message,
      };

      const postCommand = new PostToConnectionCommand({
        ConnectionId: command.receiverConnectionId,
        Data: JSON.stringify(messageData, bigIntReplacer),
      });

      await client.send(postCommand);
      showDebugConsole('Chat message sent successfully via socket');
      return true;
    } catch (error) {
      showDebugConsole(`Error sending chat message via socket: ${error.message}`);
      return false;
    }
  }
}
